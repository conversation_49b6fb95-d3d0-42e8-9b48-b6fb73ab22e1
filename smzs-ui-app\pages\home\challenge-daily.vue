<template>
  <view class="page-container modern-design">
    <!-- 动态标题栏 - 年轻化设计 -->
    <view class="modern-header" :class="{ 'scrolled': isScrolled }" :style="headerStyle">
      <view class="header-bg">
        <view class="bg-gradient"></view>
        <view class="bg-particles">
          <view v-for="i in 6" :key="i" class="particle" :style="getParticleStyle(i)"></view>
        </view>
      </view>

      <view class="header-content">
        <view class="back-button" @click="goBack">
          <view class="back-icon">
            <text class="iconfont">←</text>
          </view>
        </view>

        <view class="header-title-area">
          <text class="header-title" :class="{ 'show': showTitle }">{{ challenge.title || '挑战详情' }}</text>
          <view class="title-decoration">
            <view class="decoration-line"></view>
          </view>
        </view>

        <view class="header-actions">
          <view class="action-btn share-btn">
            <text class="iconfont">📤</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 页面内容区 -->
    <scroll-view
      class="scroll-content modern-scroll"
      scroll-y
      @scroll="handleScroll"
      :style="{ paddingTop: (statusBarHeight + 120) + 'px' }"
    >
      <!-- 今日打卡卡片 - 年轻化重设计 -->
      <view class="today-card modern-card" :class="{ 'completed': isTodayCompleted }">
        <view class="card-bg-effects">
          <view class="gradient-orb orb-1"></view>
          <view class="gradient-orb orb-2"></view>
          <view class="floating-shapes">
            <view v-for="i in 4" :key="i" class="shape" :class="`shape-${i}`"></view>
          </view>
        </view>

        <view class="today-header">
          <view class="today-badge">
            <text class="badge-emoji">🔥</text>
            <text class="badge-text">今日挑战</text>
          </view>
          <view class="today-date-modern">
            <text class="date-text">{{ formatToday() }}</text>
            <view class="date-indicator" :class="getTodayStatusClass"></view>
          </view>
        </view>

        <view class="today-main-content">
          <view class="status-display">
            <view class="status-icon" :class="getTodayStatusClass">
              <text class="status-emoji">{{ getStatusEmoji() }}</text>
            </view>
            <view class="status-info">
              <text class="status-title">{{ getTodayStatusText }}</text>
              <text class="status-subtitle">{{ getStatusSubtitle() }}</text>
            </view>
          </view>

          <view class="time-info-modern">
            <view class="time-label">
              <text class="time-icon">⏰</text>
              <text class="time-text">有效时间</text>
            </view>
            <text class="time-range">{{ formatTime(todayStatus?.validTimeRange?.startTime || challenge.startTime) }} - {{ formatTime(todayStatus?.validTimeRange?.endTime || challenge.endTime) }}</text>
          </view>

          <view v-if="todayStatus && todayStatus.record" class="checkin-result-modern">
            <view class="result-item">
              <text class="result-label">打卡时间</text>
              <text class="result-value">{{ formatDateTime(todayStatus.record.checkinTime) }}</text>
            </view>
            <view v-if="todayStatus.record.refundStatus !== '4'" class="result-item refund-info">
              <text class="result-label">退款状态</text>
              <view class="refund-status" :class="getRefundStatusClass(todayStatus.record.refundStatus)">
                <text class="refund-text">{{ getRefundStatusText(todayStatus.record.refundStatus) }}</text>
              </view>
            </view>
          </view>
        </view>

        <view v-if="!isTodayCompleted && challenge.status === '0'" class="action-area">
          <button class="modern-checkin-btn" @click="handleCheckin">
            <view class="btn-bg">
              <view class="btn-gradient"></view>
              <view class="btn-shine"></view>
            </view>
            <view class="btn-content">
              <text class="btn-emoji">⚡</text>
              <text class="btn-text">立即打卡</text>
            </view>
            <view class="btn-ripple"></view>
          </button>
        </view>
      </view>

      <!-- 今日任务详情卡片，仅当挑战有每日任务时显示 -->
      <view v-if="challenge.hasDailyTasks && dailyTask" class="daily-task-card">
        <!-- 任务头部 -->
        <view class="task-header">
          <view class="task-header-bg">
            <view class="header-wave wave1"></view>
            <view class="header-wave wave2"></view>
          </view>
          <view class="task-header-content">
            <view class="day-circle">
              <text class="day-number">{{ currentDayNumber }}</text>
              <text class="day-text">DAY</text>
            </view>
            <view class="task-header-info">
              <text class="task-category">{{ getTaskCategory(dailyTask.task) }}</text>
              <text class="task-main-title">{{ dailyTask.task.title }}</text>
              <view class="task-difficulty-indicator">
                <view
                  v-for="star in 3"
                  :key="star"
                  class="difficulty-star"
                  :class="{ 'active': star <= getTaskDifficulty(currentDayNumber) }"
                >
                  ⭐
                </view>
                <text class="difficulty-text">{{ getDifficultyText(currentDayNumber) }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 任务描述 -->
        <view class="task-description-section">
          <view class="description-icon">
            <image src="/static/images/task/description.png" class="icon" mode="aspectFit"></image>
          </view>
          <view class="description-content">
            <text class="description-title">今日目标</text>
            <text class="description-text">{{ dailyTask.task.description }}</text>
          </view>
        </view>

        <!-- 任务要求 -->
        <view v-if="dailyTask.task.taskRequirements" class="task-section enhanced">
          <view class="section-header">
            <view class="section-icon-wrapper">
              <image src="/static/images/task/check-list.png" class="section-icon" mode="aspectFit"></image>
            </view>
            <text class="section-title">任务要求</text>
            <view class="section-badge">必读</view>
          </view>
          <view class="section-content">
            {{ dailyTask.task.taskRequirements }}
          </view>
        </view>

        <!-- 小贴士 -->
        <view v-if="dailyTask.task.tips" class="task-section enhanced">
          <view class="section-header">
            <view class="section-icon-wrapper tips">
              <image src="/static/images/task/tips.png" class="section-icon" mode="aspectFit"></image>
            </view>
            <text class="section-title">成长贴士</text>
            <view class="section-badge tips">💡</view>
          </view>
          <view class="section-content tips">
            {{ dailyTask.task.tips }}
          </view>
        </view>

        <!-- 相关资源 -->
        <view v-if="dailyTask.task.resourceUrls" class="task-section enhanced">
          <view class="section-header">
            <view class="section-icon-wrapper resources">
              <image src="/static/images/task/resources.png" class="section-icon" mode="aspectFit"></image>
            </view>
            <text class="section-title">学习资源</text>
            <view class="section-badge resources">{{ formatResourceUrls(dailyTask.task.resourceUrls).length }}</view>
          </view>
          <view class="section-content resources">
            <view v-for="(url, index) in formatResourceUrls(dailyTask.task.resourceUrls)"
                  :key="index"
                  class="resource-item enhanced"
                  @tap="openResource(url)">
              <view class="resource-icon-wrapper">
                <image src="/static/images/task/link.png" class="resource-icon" mode="aspectFit"></image>
              </view>
              <view class="resource-content">
                <text class="resource-title">学习资源 {{ index + 1 }}</text>
                <text class="resource-desc">点击查看详细内容</text>
              </view>
              <view class="resource-arrow">
                <image src="/static/images/challenge/arrow-right.png" class="arrow-icon" mode="aspectFit"></image>
              </view>
            </view>
          </view>
        </view>

        <!-- 完成状态或激励信息 -->
        <view class="task-status-section">
          <view v-if="dailyTask.isCompleted" class="task-completion-status completed">
            <view class="completion-icon">
              <image src="/static/images/challenge/check-circle.png" class="status-icon" mode="aspectFit"></image>
            </view>
            <view class="completion-content">
              <text class="completion-title">🎉 今日任务已完成</text>
              <text class="completion-desc">你今天表现得很棒，继续保持！</text>
            </view>
          </view>
          <view v-else class="task-motivation">
            <view class="motivation-icon">
              <image src="/static/images/challenge/rocket.png" class="motivation-img" mode="aspectFit"></image>
            </view>
            <view class="motivation-content">
              <text class="motivation-title">{{ getMotivationTitle(currentDayNumber) }}</text>
              <text class="motivation-desc">{{ getMotivationDesc(currentDayNumber) }}</text>
            </view>
          </view>
        </view>

        <!-- 进度指示器 - 优化样式 -->
        <view class="task-progress-section enhanced">
          <view class="progress-header">
            <view class="progress-title-wrapper">
              <image src="/static/images/challenge/progress.png" class="progress-icon" mode="aspectFit"></image>
              <text class="progress-title">挑战进度</text>
            </view>
            <view class="progress-value-wrapper">
              <text class="progress-value">{{ currentDayNumber }}</text>
              <text class="progress-separator">/</text>
              <text class="progress-total">{{ challenge.durationDays || 21 }}</text>
            </view>
          </view>
          <view class="progress-track enhanced">
            <view class="progress-fill enhanced" :style="{ width: taskProgressWidth }">
              <view class="progress-shine"></view>
            </view>
            <view class="progress-marker enhanced" :style="{ left: taskProgressWidth }">
              <view class="marker-dot enhanced">
                <view class="marker-pulse"></view>
              </view>
            </view>
          </view>
          <view class="progress-milestones enhanced">
            <view
              v-for="milestone in getProgressMilestones()"
              :key="milestone.day"
              class="milestone enhanced"
              :class="{ 'completed': currentDayNumber >= milestone.day }"
              :style="{ left: milestone.position }"
            >
              <view class="milestone-dot enhanced"></view>
              <text class="milestone-text enhanced">{{ milestone.text }}</text>
            </view>
          </view>
        </view>
      </view>



      <!-- 统计数据卡片 -->
      <view class="stats-section">
        <text class="section-title">数据概览</text>
        <view class="stats-grid">
          <view class="stats-item">
            <view class="stats-icon" style="background: rgba(86, 119, 252, 0.1)">
              <image src="/static/images/challenge/leiji.png" mode="aspectFit"></image>
            </view>
            <text class="stats-value">{{ stats.keepDays }}</text>
            <text class="stats-label">累计打卡</text>
          </view>
          <view class="stats-item">
            <view class="stats-icon" style="background: rgba(255, 107, 107, 0.1)">
              <image src="/static/images/challenge/fire.png" mode="aspectFit"></image>
            </view>
            <text class="stats-value">{{ stats.totalEnergy }}</text>
            <text class="stats-label">累计能量</text>
          </view>
          <view class="stats-item">
            <view class="stats-icon" style="background: rgba(255, 215, 0, 0.1)">
              <image src="/static/images/challenge/medal.png" mode="aspectFit"></image>
            </view>
            <text class="stats-value">{{ stats.maxContinuousDays }}</text>
            <text class="stats-label">最长连续</text>
          </view>
          <view class="stats-item">
            <view class="stats-icon" style="background: rgba(32, 201, 151, 0.1)">
              <image src="/static/images/challenge/star.png" mode="aspectFit"></image>
            </view>
            <text class="stats-value">{{ (stats.completionRate * 100).toFixed(1) }}%</text>
            <text class="stats-label">完成率</text>
          </view>
        </view>
      </view>

      <!-- 打卡记录日历 -->
      <view class="calendar-section">
        <text class="section-title">打卡记录</text>
        <view class="calendar-card">
          <view class="weekdays">
            <text v-for="day in ['日', '一', '二', '三', '四', '五', '六']" :key="day">{{ day }}</text>
          </view>
          <view class="days-grid">
            <view 
              v-for="(day, index) in periodDays" 
              :key="index"
              class="day-item"
              :class="{
                'checked': day.checked,
                'invalid-time': day.invalidTime,
                'today': day.isToday,
                'in-period': day.inPeriod,
                'failed': day.failed,
                'placeholder': day.isPlaceholder
              }"
            >
              <template v-if="!day.isPlaceholder">
                <text class="day-text">{{ day.date }}</text>
                <text class="month-text" v-if="day.showMonth">{{ day.month }}月</text>
                <view v-if="day.checked" class="check-mark">
                  <image src="/static/images/common/check.png" mode="aspectFit"></image>
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>

      <!-- 平均打卡时段 -->
      <view class="time-section">
        <text class="section-title">打卡时段</text>
        <view class="time-card">
          <view class="time-icon">
            <image src="/static/images/challenge/clock.png" mode="aspectFit"></image>
          </view>
          <view class="time-info">
            <text class="time-value">{{ stats.averageCheckinTime || '暂无数据' }}</text>
            <text class="time-label">平均打卡时间</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 彩屑爆炸效果 -->
    <view v-if="showConfetti" class="confetti-container">
      <view v-for="i in 50" :key="i" class="confetti-piece" :style="getConfettiStyle(i)"></view>
    </view>

    <!-- 现代化打卡成功弹窗 -->
    <view v-if="checkinPopup" class="modern-modal-mask" @click="closeCheckinPopup">
      <view class="modern-modal" :class="getModalClass()" @click.stop>
        <view class="modal-bg-effects">
          <view class="celebration-particles">
            <view v-for="i in 12" :key="i" class="particle" :style="getParticleStyle(i)"></view>
          </view>
          <view class="modal-gradient"></view>
        </view>

        <view class="modal-content-modern">
          <view class="modal-icon-area">
            <view class="icon-container" :class="getModalClass()">
              <text class="modal-emoji">{{ getModalEmoji() }}</text>
              <view class="icon-ring"></view>
              <view class="icon-pulse"></view>
            </view>
          </view>

          <view class="modal-text-area">
            <text class="modal-title-modern">{{ getModalTitle() }}</text>
            <text class="modal-subtitle">{{ getModalSubtitle() }}</text>
            <text class="modal-description">{{ getModalDescription() }}</text>
          </view>

          <view class="modal-actions">
            <button class="modern-modal-btn" :class="getModalClass()" @click="closeCheckinPopup">
              <view class="btn-bg-modern">
                <view class="btn-gradient-modern"></view>
                <view class="btn-shine-modern"></view>
              </view>
              <text class="btn-text-modern">{{ getModalButtonText() }}</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/index.vue'
import { getChallengeStats, checkInTask, getTodayTaskStatus, getChallengeTask } from '@/api/index'

export default {
  components: {
    CustomNavbar
  },

  data() {
    return {
      statusBarHeight: 20,
      challenge: {},
      stats: {
        keepDays: 0,
        completionRate: 0,
        totalEnergy: 0,
        maxContinuousDays: 0,
        averageCheckinTime: ''
      },
      selectedDates: [],
      checkinRemark: '',
      isTodayCompleted: false,
      todayStatus: null,
      currentMonth: '',
      calendarDays: [],
      isLoading: true,
      checkinPopup: false,
      isValidTime: true,
      dailyTask: null,
      currentDayNumber: 0,
      showConfetti: false,
      // 现代化UI控制
      isScrolled: false,
      showTitle: false,
      lastScrollTop: 0,
      headerHeight: 120,
      scrollY: 0
    }
  },

  computed: {
    // 头部样式
    headerStyle() {
      const opacity = Math.min(this.scrollY / 100, 0.95)
      return {
        background: `linear-gradient(135deg,
          rgba(138, 43, 226, ${0.8 + opacity * 0.2}) 0%,
          rgba(30, 144, 255, ${0.8 + opacity * 0.2}) 50%,
          rgba(255, 20, 147, ${0.8 + opacity * 0.2}) 100%)`,
        backdropFilter: `blur(${10 + this.scrollY * 0.1}px)`
      }
    },

    getTodayStatusText() {
      if (this.isLoading) return '加载中...'
      if (this.challenge.status !== '0') return '挑战已结束'
      if (!this.todayStatus || !this.todayStatus.record) return '等待打卡'
      if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {
        return this.todayStatus.record.isValidTime ? '打卡成功 🎉' : '时间无效 😅'
      } else if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {
        return '打卡失败 😔'
      } else {
        return '等待打卡'
      }
    },

    getTodayStatusClass() {
      if (this.isLoading) return 'status-loading'
      if (this.challenge.status !== '0') return 'status-ended'
      if (!this.todayStatus) return 'status-pending'
      if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {
        return this.todayStatus.record.isValidTime ? 'status-success' : 'status-warning'
      } else if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {
        return 'status-failed'
      } else {
        return 'status-pending'
      }
    },

    progressWidth() {
      if (!this.challenge.ruleDays) return '0%'
      return `${(this.stats.keepDays / this.challenge.ruleDays * 100).toFixed(1)}%`
    },

    periodDays() {
      if (!this.challenge.startDate || !this.challenge.endDate) return []
      
      const startDate = new Date(this.challenge.startDate)
      const endDate = new Date(this.challenge.endDate)
      const days = []
      
      // 计算开始日期是星期几 (0-6, 0表示周日)
      const startDayOfWeek = startDate.getDay()
      
      // 添加前面的空白占位符
      for (let i = 0; i < startDayOfWeek; i++) {
        days.push({
          date: '',
          month: 0,
          showMonth: false,
          checked: false,
          isToday: false,
          inPeriod: false,
          failed: false,
          isPlaceholder: true
        })
      }
      
      let currentDate = new Date(startDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      while (currentDate <= endDate) {
        const isStartMonth = currentDate.getDate() === 1 || 
                            currentDate.getTime() === startDate.getTime()
        
        const isFailed = currentDate < today && 
                        !this.isDateChecked(currentDate) && 
                        this.challenge.status === '0'
        
        days.push({
          date: currentDate.getDate(),
          month: currentDate.getMonth() + 1,
          showMonth: isStartMonth,
          checked: this.isDateChecked(currentDate),
          isToday: currentDate.toDateString() === today.toDateString(),
          inPeriod: true,
          failed: isFailed,
          fullDate: new Date(currentDate),
          dayOfWeek: currentDate.getDay()  // 添加星期几信息 (0-6)
        })
        
        currentDate.setDate(currentDate.getDate() + 1)
      }
      
      return days
    },

    // 计算任务进度宽度
    taskProgressWidth() {
      const totalDays = this.challenge.durationDays || 21
      const progress = (this.currentDayNumber / totalDays) * 100
      return `${Math.min(progress, 100)}%`
    }
  },

  onPullDownRefresh() {
    this.loadData().then(() => {
      uni.stopPullDownRefresh();
    }).catch(() => {
      uni.stopPullDownRefresh();
    });
  },

  onLoad(options) {
    // 获取状态栏高度和安全区域
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 20

    // 设置安全区域高度，包含状态栏高度
    this.headerHeight = this.statusBarHeight + 100

    if (options.selectedCard) {
      this.challenge = JSON.parse(decodeURIComponent(options.selectedCard))
      this.loadData()
      this.initCalendar()
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    // 处理滚动事件 - 现代化动画效果
    handleScroll(e) {
      const scrollTop = e.detail.scrollTop
      this.scrollY = scrollTop

      // 控制头部状态
      this.isScrolled = scrollTop > 50
      this.showTitle = scrollTop > 80

      this.lastScrollTop = scrollTop
    },

    // 获取粒子样式
    getParticleStyle(index) {
      const positions = [
        { left: '10%', top: '20%', animationDelay: '0s' },
        { left: '80%', top: '15%', animationDelay: '1s' },
        { left: '60%', top: '40%', animationDelay: '2s' },
        { left: '20%', top: '60%', animationDelay: '0.5s' },
        { left: '90%', top: '70%', animationDelay: '1.5s' },
        { left: '40%', top: '80%', animationDelay: '2.5s' }
      ]
      return positions[index - 1] || {}
    },

    // 获取彩屑样式
    getConfettiStyle(index) {
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
      const color = colors[index % colors.length]
      const left = Math.random() * 100
      const animationDelay = Math.random() * 3
      const animationDuration = 3 + Math.random() * 2
      const rotation = Math.random() * 360

      return {
        backgroundColor: color,
        left: left + '%',
        animationDelay: animationDelay + 's',
        animationDuration: animationDuration + 's',
        transform: `rotate(${rotation}deg)`
      }
    },

    // 获取状态表情
    getStatusEmoji() {
      const statusClass = this.getTodayStatusClass
      const emojiMap = {
        'status-success': '🎉',
        'status-warning': '⚠️',
        'status-failed': '😔',
        'status-pending': '⏳',
        'status-ended': '🏁',
        'status-loading': '⏳'
      }
      return emojiMap[statusClass] || '⏳'
    },

    // 获取状态副标题
    getStatusSubtitle() {
      const statusClass = this.getTodayStatusClass
      const subtitleMap = {
        'status-success': '太棒了！继续保持',
        'status-warning': '时间不对，下次注意',
        'status-failed': '没关系，明天再来',
        'status-pending': '准备好了吗？',
        'status-ended': '挑战已完成',
        'status-loading': '正在加载...'
      }
      return subtitleMap[statusClass] || '加油！'
    },

    // 获取退款状态样式类
    getRefundStatusClass(status) {
      const classMap = {
        '0': 'refund-pending',
        '1': 'refund-success',
        '2': 'refund-failed',
        '3': 'refund-processing'
      }
      return classMap[status] || ''
    },

    // 获取退款状态文本
    getRefundStatusText(status) {
      const textMap = {
        '0': '退款待处理',
        '1': '已退款',
        '2': '退款失败',
        '3': '退款处理中'
      }
      return textMap[status] || '未知状态'
    },

    async loadData() {
      try {
        this.isLoading = true
        uni.showLoading({ title: '加载中...' })
        
        // 获取今日状态
        const statusRes = await getTodayTaskStatus(this.challenge.participationId)
        if (statusRes.code === 200) {
          this.todayStatus = statusRes.data
          this.isTodayCompleted = this.todayStatus?.record
        }

        // 获取统计数据
        const statsRes = await getChallengeStats(this.challenge.participationId)
        if (statsRes.code === 200) {
          this.stats = statsRes.data
          this.updateCalendarCheckedDays(this.stats.checkinDates)
        }

        // 如果挑战有每日任务，获取当天任务详情
        if (this.challenge.hasDailyTasks) {
          // 计算当前是参与挑战后的第几天
          const today = new Date()
          const startDate = new Date(this.challenge.startDate)
          const dayDiff = Math.floor((today - startDate) / (1000 * 60 * 60 * 24)) + 1
          this.currentDayNumber = Math.min(Math.max(1, dayDiff), 21) // 确保在1-21天范围内
          
          try {
            const taskRes = await getChallengeTask(this.challenge.participationId, this.currentDayNumber)
            if (taskRes.code === 200) {
              this.dailyTask = taskRes.data
            }
          } catch (error) {
            console.error('获取每日任务失败:', error)
          }
        }

        uni.hideLoading()
        this.isLoading = false
      } catch (error) {
        uni.hideLoading()
        this.isLoading = false
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    formatToday() {
      const today = new Date()
      return `${today.getMonth() + 1}月${today.getDate()}日`
    },

    formatDateRange(start, end) {
      if (!start || !end) return ''
      const startDate = new Date(start)
      const endDate = new Date(end)
      return `${startDate.getMonth() + 1}月${startDate.getDate()}日 - ${endDate.getMonth() + 1}月${endDate.getDate()}日`
    },

    formatDateTime(time) {
      if (!time) return '--:--'
      const date = new Date(time)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    },

    formatTime(dateStr) {
        if (!dateStr) return '00:00';
        const timeStr = dateStr.split(' ')[1] || dateStr;
        const timeParts = timeStr.split(':');
        if (timeParts.length >= 2) {
            return `${timeParts[0]}:${timeParts[1]}`;
        }
        return timeStr;
    },

    initCalendar() {
      const startDate = new Date(this.challenge.startDate)
      const endDate = new Date(this.challenge.endDate)
      const now = new Date()
      
      // 默认显示当前月份,如果当前月不在挑战周期内,则显示开始月
      let currentDate = now
      if (now < startDate || now > endDate) {
        currentDate = startDate
      }
      
      this.currentMonth = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`
      this.generateCalendarDays(currentDate)
    },

    generateCalendarDays(date) {
      const year = date.getFullYear()
      const month = date.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      
      const startDate = new Date(this.challenge.startDate)
      const endDate = new Date(this.challenge.endDate)
      
      const days = []
      const startOffset = firstDay.getDay()
      
      // 上个月的天数
      for (let i = startOffset - 1; i >= 0; i--) {
        const prevDate = new Date(year, month, -i)
        days.push({
          date: prevDate.getDate(),
          currentMonth: false,
          checked: false,
          isToday: false,
          fullDate: prevDate,
          inRange: prevDate >= startDate && prevDate <= endDate
        })
      }
      
      // 当前月的天数
      const today = new Date()
      for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i)
        days.push({
          date: i,
          currentMonth: true,
          checked: false,
          isToday: currentDate.toDateString() === today.toDateString(),
          fullDate: currentDate,
          inRange: currentDate >= startDate && currentDate <= endDate
        })
      }
      
      // 下个月的天数
      const remainingDays = 42 - days.length
      for (let i = 1; i <= remainingDays; i++) {
        const nextDate = new Date(year, month + 1, i)
        days.push({
          date: i,
          currentMonth: false,
          checked: false,
          isToday: false,
          fullDate: nextDate,
          inRange: nextDate >= startDate && nextDate <= endDate
        })
      }
      
      this.calendarDays = days
    },

    updateCalendarCheckedDays(checkedDates) {
      if (!checkedDates || !checkedDates.length) return
      
      if (this.stats.dailyRecords && this.stats.dailyRecords.length > 0) {
        this.stats.dailyRecords.forEach(daily => {
          const checkedDate = new Date(daily.checkinDate)
          const dayIndex = this.calendarDays.findIndex(day => 
            day.fullDate.toDateString() === checkedDate.toDateString()
          )
          if (dayIndex !== -1) {
            this.calendarDays[dayIndex].checked = true
            this.calendarDays[dayIndex].invalidTime = !daily.isValidTime
          }
        })
      } else {
        // 向后兼容，没有详细记录的情况
        checkedDates.forEach(dateStr => {
          const checkedDate = new Date(dateStr)
          const dayIndex = this.calendarDays.findIndex(day => 
            day.fullDate.toDateString() === checkedDate.toDateString()
          )
          if (dayIndex !== -1) {
            this.calendarDays[dayIndex].checked = true
          }
        })
      }
    },

    prevMonth() {
      const [year, month] = this.currentMonth.replace(/[年月]/g, '-').split('-')
      const prevDate = new Date(parseInt(year), parseInt(month) - 2)
      
      // 检查是否超出挑战周期
      const startDate = new Date(this.challenge.startDate)
      if (prevDate < new Date(startDate.getFullYear(), startDate.getMonth(), 1)) {
        return
      }
      
      this.currentMonth = `${prevDate.getFullYear()}年${prevDate.getMonth() + 1}月`
      this.generateCalendarDays(prevDate)
      this.updateCalendarCheckedDays(this.stats.checkinDates)
    },

    nextMonth() {
      const [year, month] = this.currentMonth.replace(/[年月]/g, '-').split('-')
      const nextDate = new Date(parseInt(year), parseInt(month))
      
      // 检查是否超出挑战周期
      const endDate = new Date(this.challenge.endDate)
      if (nextDate > new Date(endDate.getFullYear(), endDate.getMonth(), 1)) {
        return
      }
      
      this.currentMonth = `${nextDate.getFullYear()}年${nextDate.getMonth() + 1}月`
      this.generateCalendarDays(nextDate)
      this.updateCalendarCheckedDays(this.stats.checkinDates)
    },

    async handleCheckin() {
      if (this.challenge.status !== '0') {
        uni.showToast({
          title: '任务已结束',
          icon: 'none'
        })
        return
      }
      if (this.isTodayCompleted) {
        uni.showToast({
          title: '今日已完成打卡',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: '打卡中...' })
        const res = await checkInTask({
          participationId: this.challenge.participationId,
          remark: ''
        })

        uni.hideLoading()
        if (res.code === 200) {
          this.isTodayCompleted = true
          this.isValidTime = res.data.isValidTime || false
          await this.loadData()

          // 先显示彩屑爆炸效果
          this.showConfetti = true

          // 1.5秒后隐藏彩屑并显示弹窗
          setTimeout(() => {
            this.showConfetti = false
            this.checkinPopup = true
          }, 1500)
        } else {
          throw new Error(res.msg)
        }
      } catch (error) {
        console.log('打卡错误:', error)
        uni.hideLoading()
        
        // 获取错误信息（兼容多种格式）
        let errorMsg = ''
        if (error.message) {
          errorMsg = error.message
        } else if (typeof error === 'string') {
          errorMsg = error
        } else if (error.msg) {
          errorMsg = error.msg
        } else {
          errorMsg = '打卡失败'
        }
        
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      }
    },

    closeCheckinPopup() {
      this.checkinPopup = false
    },

    isDateChecked(date) {
      if (!this.stats.checkinDates) return false
      return this.stats.checkinDates.some(checkedDate => 
        new Date(checkedDate).toDateString() === date.toDateString()
      )
    },

    // 获取弹窗样式类
    getModalClass() {
      const statusClass = this.getTodayStatusClass
      if (statusClass === 'status-success') return 'modal-success'
      if (statusClass === 'status-warning') return 'modal-warning'
      if (statusClass === 'status-failed') return 'modal-failed'
      return 'modal-default'
    },

    // 获取弹窗表情
    getModalEmoji() {
      const statusClass = this.getTodayStatusClass
      const emojiMap = {
        'status-success': '🎉',
        'status-warning': '⚠️',
        'status-failed': '😔',
        'status-pending': '⏳'
      }
      return emojiMap[statusClass] || '🎉'
    },

    // 获取弹窗标题
    getModalTitle() {
      const statusClass = this.getTodayStatusClass
      const titleMap = {
        'status-success': '打卡成功！',
        'status-warning': '时间不对哦',
        'status-failed': '打卡失败',
        'status-pending': '准备打卡'
      }
      return titleMap[statusClass] || '打卡完成'
    },

    // 获取弹窗副标题
    getModalSubtitle() {
      const statusClass = this.getTodayStatusClass
      const subtitleMap = {
        'status-success': '太棒了！你今天表现很棒',
        'status-warning': '不在有效时间内',
        'status-failed': '没关系，明天再来',
        'status-pending': '加油！'
      }
      return subtitleMap[statusClass] || '继续努力'
    },

    // 获取弹窗描述
    getModalDescription() {
      const timeRange = `${this.formatTime(this.todayStatus?.validTimeRange?.startTime || this.challenge.startTime)}-${this.formatTime(this.todayStatus?.validTimeRange?.endTime || this.challenge.endTime)}`
      const statusClass = this.getTodayStatusClass

      if (statusClass === 'status-success') {
        return '你干得不错，比昨天的你强了不少！继续保持这个节奏，成功就在前方等着你。'
      } else if (statusClass === 'status-warning') {
        return `有效打卡时间是 ${timeRange}，记得在正确时间打卡哦！`
      } else if (statusClass === 'status-failed') {
        return `错过了 ${timeRange} 的有效时间，不过没关系，明天记得早点来！`
      }
      return '每一次坚持都是成长，加油！'
    },

    // 获取弹窗按钮文本
    getModalButtonText() {
      const statusClass = this.getTodayStatusClass
      const textMap = {
        'status-success': '我还能更狠一点 💪',
        'status-warning': '下次注意时间 ⏰',
        'status-failed': '明天再来挑战 🔥',
        'status-pending': '开始挑战 🚀'
      }
      return textMap[statusClass] || '继续加油 💪'
    },

    formatResourceUrls(urls) {
      if (!urls) return [];
      
      // 如果urls是字符串，尝试解析为数组
      let urlArray = urls;
      if (typeof urls === 'string') {
        try {
          // 尝试解析为JSON
          urlArray = JSON.parse(urls);
        } catch (e) {
          // 如果不是合法的JSON，可能是以逗号分隔的字符串
          urlArray = urls.split(',');
        }
      }
      
      if (!Array.isArray(urlArray)) {
        urlArray = [urlArray.toString()];
      }
      
      return urlArray;
    },

    openResource(url) {
      // 检查URL是否有http/https前缀，如果没有则添加
      let formattedUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        formattedUrl = 'https://' + url;
      }

      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(formattedUrl)}`
      });
    },

    // 获取任务分类
    getTaskCategory(task) {
      if (!task || !task.title) return '成长任务'

      const title = task.title.toLowerCase()
      if (title.includes('冥想') || title.includes('meditation')) return '冥想修行'
      if (title.includes('运动') || title.includes('exercise')) return '运动健身'
      if (title.includes('阅读') || title.includes('reading')) return '知识学习'
      if (title.includes('反思') || title.includes('reflection')) return '自我反思'
      if (title.includes('写作') || title.includes('writing')) return '创作表达'
      if (title.includes('社交') || title.includes('social')) return '人际交往'

      return '成长任务'
    },

    // 获取任务难度
    getTaskDifficulty(dayNumber) {
      if (dayNumber <= 7) return 1 // 第一周：简单
      if (dayNumber <= 14) return 2 // 第二周：中等
      return 3 // 第三周：困难
    },

    // 获取难度文本
    getDifficultyText(dayNumber) {
      const difficulty = this.getTaskDifficulty(dayNumber)
      const texts = ['', '入门', '进阶', '挑战']
      return texts[difficulty] || '入门'
    },

    // 获取激励标题
    getMotivationTitle(dayNumber) {
      const titles = [
        '开始你的成长之旅！',
        '坚持就是胜利！',
        '你已经走了很远！',
        '继续保持这个节奏！',
        '你正在变得更好！',
        '每一天都是新的开始！',
        '第一周即将完成！',
        '恭喜完成第一周！',
        '进入第二阶段！',
        '你的坚持令人敬佩！',
        '已经过半了！',
        '你比想象中更强大！',
        '距离目标越来越近！',
        '第二周即将完成！',
        '进入最后冲刺阶段！',
        '你已经证明了自己！',
        '坚持到现在真不容易！',
        '胜利就在眼前！',
        '最后几天了！',
        '明天就是最后一天！',
        '恭喜完成21天挑战！'
      ]
      return titles[dayNumber - 1] || '继续加油！'
    },

    // 获取激励描述
    getMotivationDesc(dayNumber) {
      if (dayNumber <= 7) {
        return '新习惯的养成需要时间，每一天的坚持都在为未来的自己投资。'
      } else if (dayNumber <= 14) {
        return '你已经度过了最困难的适应期，现在是建立稳定习惯的关键时期。'
      } else {
        return '你即将完成这个了不起的挑战，这份坚持将成为你人生的宝贵财富。'
      }
    },

    // 获取进度里程碑
    getProgressMilestones() {
      const totalDays = this.challenge.durationDays || 21
      return [
        { day: 1, text: '开始', position: '0%' },
        { day: Math.floor(totalDays / 3), text: '1/3', position: '33.33%' },
        { day: Math.floor(totalDays * 2 / 3), text: '2/3', position: '66.66%' },
        { day: totalDays, text: '完成', position: '100%' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
/* 现代化页面容器 - 马卡龙风格 */
.page-container.modern-design {
  min-height: 100vh;
  background: #f8f8fa;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(255, 184, 209, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(130, 215, 247, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(255, 223, 128, 0.3) 0%, transparent 50%);
    z-index: 0;
  }

  &::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(1px);
    z-index: 0;
  }
}

/* 彩屑爆炸效果 */
.confetti-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 999;
  overflow: hidden;
}

.confetti-piece {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  top: -20rpx;
  animation: confetti-fall linear forwards;
  border-radius: 4rpx;
  opacity: 0.9;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotateZ(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotateZ(720deg);
    opacity: 0;
  }
}

@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

/* 现代化头部 */
.modern-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 20px) + 120rpx);
  z-index: 100;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &.scrolled {
    height: calc(var(--status-bar-height, 20px) + 100rpx);

    .header-content {
      padding: calc(var(--status-bar-height, 20px) + 20rpx) 32rpx 20rpx;
    }
  }

  .header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;

    .bg-gradient {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(138, 43, 226, 0.9) 0%,
        rgba(30, 144, 255, 0.9) 50%,
        rgba(255, 20, 147, 0.9) 100%);
      backdrop-filter: blur(20rpx);
      -webkit-backdrop-filter: blur(20rpx);
    }

    .bg-particles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .particle {
        position: absolute;
        width: 8rpx;
        height: 8rpx;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        animation: float 3s ease-in-out infinite;
      }
    }
  }

  .header-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: calc(var(--status-bar-height, 20px) + 30rpx) 40rpx 30rpx;

    .back-button {
      width: 80rpx;
      height: 80rpx;
      border-radius: 40rpx;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10rpx);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.9);
        background: rgba(255, 255, 255, 0.3);
      }

      .back-icon {
        font-size: 36rpx;
        color: #fff;
        font-weight: bold;
      }
    }

    .header-title-area {
      flex: 1;
      text-align: center;
      position: relative;

      .header-title {
        font-size: 36rpx;
        font-weight: 700;
        color: #fff;
        opacity: 0;
        transform: translateY(10rpx);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &.show {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .title-decoration {
        position: absolute;
        bottom: -10rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;

        .decoration-line {
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, #fff, transparent);
          border-radius: 2rpx;
          opacity: 0;
          animation: slideIn 0.5s ease 0.3s forwards;
        }
      }
    }

    .header-actions {
      width: 80rpx;
      display: flex;
      justify-content: flex-end;

      .action-btn {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10rpx);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.9);
          background: rgba(255, 255, 255, 0.3);
        }

        .iconfont {
          font-size: 32rpx;
        }
      }
    }
  }
}

/* 现代化滚动内容 */
.scroll-content.modern-scroll {
  height: 100vh;
  box-sizing: border-box;
  padding: 32rpx;
  position: relative;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 现代化今日打卡卡片 */
.today-card.modern-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 40rpx;
  padding: 48rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    0 8rpx 32rpx rgba(138, 43, 226, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    transform: translateY(-8rpx);
    box-shadow:
      0 32rpx 80rpx rgba(0, 0, 0, 0.15),
      0 16rpx 48rpx rgba(138, 43, 226, 0.2),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  }

  &.completed {
    background: rgba(76, 175, 80, 0.15);
    border-color: rgba(76, 175, 80, 0.3);

    .card-bg-effects .gradient-orb {
      background: radial-gradient(circle, rgba(76, 175, 80, 0.3) 0%, transparent 70%);
    }
  }

  .card-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;

    .gradient-orb {
      position: absolute;
      border-radius: 50%;
      filter: blur(40rpx);
      animation: float 6s ease-in-out infinite;

      &.orb-1 {
        width: 200rpx;
        height: 200rpx;
        top: -50rpx;
        right: -50rpx;
        background: radial-gradient(circle, rgba(255, 20, 147, 0.3) 0%, transparent 70%);
        animation-delay: 0s;
      }

      &.orb-2 {
        width: 150rpx;
        height: 150rpx;
        bottom: -30rpx;
        left: -30rpx;
        background: radial-gradient(circle, rgba(30, 144, 255, 0.3) 0%, transparent 70%);
        animation-delay: 3s;
      }
    }

    .floating-shapes {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .shape {
        position: absolute;
        background: rgba(255, 255, 255, 0.1);
        animation: floatShape 8s ease-in-out infinite;

        &.shape-1 {
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;
          top: 20%;
          left: 10%;
          animation-delay: 0s;
        }

        &.shape-2 {
          width: 16rpx;
          height: 16rpx;
          border-radius: 4rpx;
          top: 60%;
          right: 15%;
          animation-delay: 2s;
        }

        &.shape-3 {
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
          bottom: 30%;
          left: 20%;
          animation-delay: 4s;
        }

        &.shape-4 {
          width: 18rpx;
          height: 18rpx;
          border-radius: 6rpx;
          top: 40%;
          right: 30%;
          animation-delay: 6s;
        }
      }
    }
  }

  /* 今日头部区域 */
  .today-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    position: relative;
    z-index: 1;

    .today-badge {
      display: flex;
      align-items: center;
      padding: 16rpx 24rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50rpx;
      backdrop-filter: blur(10rpx);

      .badge-emoji {
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      .badge-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #fff;
      }
    }

    .today-date-modern {
      display: flex;
      align-items: center;

      .date-text {
        font-size: 32rpx;
        font-weight: 700;
        color: #fff;
        margin-right: 16rpx;
      }

      .date-indicator {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        animation: pulse 2s ease-in-out infinite;

        &.status-success {
          background: #4CAF50;
        }

        &.status-warning {
          background: #FF9800;
        }

        &.status-failed {
          background: #F44336;
        }

        &.status-pending {
          background: #2196F3;
        }
      }
    }
  }

  /* 主要内容区域 */
  .today-main-content {
    position: relative;
    z-index: 1;

    .status-display {
      display: flex;
      align-items: center;
      margin-bottom: 32rpx;
      padding: 24rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 24rpx;
      backdrop-filter: blur(10rpx);

      .status-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        background: rgba(255, 255, 255, 0.2);

        &.status-success {
          background: linear-gradient(135deg, #4CAF50, #66BB6A);
        }

        &.status-warning {
          background: linear-gradient(135deg, #FF9800, #FFB74D);
        }

        &.status-failed {
          background: linear-gradient(135deg, #F44336, #EF5350);
        }

        &.status-pending {
          background: linear-gradient(135deg, #2196F3, #42A5F5);
        }

        .status-emoji {
          font-size: 40rpx;
        }
      }

      .status-info {
        flex: 1;

        .status-title {
          font-size: 32rpx;
          font-weight: 700;
          color: #fff;
          margin-bottom: 8rpx;
          display: block;
        }

        .status-subtitle {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
          display: block;
        }
      }
    }

    .time-info-modern {
      margin-bottom: 24rpx;
      padding: 20rpx 24rpx;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 20rpx;
      border: 1rpx solid rgba(255, 255, 255, 0.1);

      .time-label {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .time-icon {
          font-size: 24rpx;
          margin-right: 8rpx;
        }

        .time-text {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .time-range {
        font-size: 28rpx;
        font-weight: 600;
        color: #fff;
      }
    }

    .checkin-result-modern {
      .result-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx 24rpx;
        margin-bottom: 12rpx;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 16rpx;

        .result-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }

        .result-value {
          font-size: 26rpx;
          font-weight: 600;
          color: #fff;
        }

        &.refund-info {
          .refund-status {
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            font-size: 22rpx;

            &.refund-pending {
              background: rgba(255, 152, 0, 0.2);
              color: #FF9800;
            }

            &.refund-success {
              background: rgba(76, 175, 80, 0.2);
              color: #4CAF50;
            }

            &.refund-failed {
              background: rgba(244, 67, 54, 0.2);
              color: #F44336;
            }

            &.refund-processing {
              background: rgba(33, 150, 243, 0.2);
              color: #2196F3;
            }
          }
        }
      }
    }
  }

  .time-range-info {
    margin-bottom: 20rpx;
    position: relative;
    z-index: 1;

    .time-range-text {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .checkin-result-info {
    margin-bottom: 30rpx;
    padding: 20rpx;
    background: #F9F9F9;
    border-radius: 16rpx;
    font-size: 26rpx;
    
    .checkin-time {
      margin-bottom: 10rpx;
      color: #333;
    }
    
    .checkin-status {
      display: inline-block;
      padding: 4rpx 16rpx;
      border-radius: 20rpx;
      margin-bottom: 10rpx;
      font-size: 24rpx;
      
      &.valid {
        background: rgba(39, 174, 96, 0.1);
        color: #27AE60;
      }
      
      &.invalid {
        background: rgba(235, 87, 87, 0.1);
        color: #EB5757;
      }
    }
    
    .energy-info {
      display: flex;
      justify-content: space-between;
      color: #666;
      
      .refund-pending {
        color: #FF9F1C;
      }
      
      .refund-done {
        color: #27AE60;
      }
      
      .refund-failed {
        color: #EB5757;
      }
      
      .refund-processing {
        color: #2F80ED;
      }
      
      .refund-none {
        color: #999;
      }
    }
  }

  /* 现代化操作区域 */
  .action-area {
    position: relative;
    z-index: 1;
    margin-top: 32rpx;

    .modern-checkin-btn {
      width: 100%;
      height: 120rpx;
      position: relative;
      border-radius: 60rpx;
      overflow: hidden;
      border: none;
      background: transparent;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:active {
        transform: scale(0.95);
      }

      .btn-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 60rpx;
        overflow: hidden;

        .btn-gradient {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            #FF6B6B 0%,
            #4ECDC4 25%,
            #45B7D1 50%,
            #96CEB4 75%,
            #FFEAA7 100%);
          background-size: 300% 300%;
          animation: gradientShift 3s ease infinite;
        }

        .btn-shine {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.4) 50%,
            transparent 100%);
          animation: shine 2s ease-in-out infinite;
        }
      }

      .btn-content {
        position: relative;
        z-index: 2;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .btn-emoji {
          font-size: 48rpx;
          margin-right: 16rpx;
          animation: bounce 1s ease-in-out infinite;
        }

        .btn-text {
          font-size: 36rpx;
          font-weight: 700;
          color: #fff;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }
      }

      .btn-ripple {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: all 0.6s ease;
      }

      &:active .btn-ripple {
        width: 300rpx;
        height: 300rpx;
      }
    }
  }
}
}

.daily-task-card {
  background: #fff;
  border-radius: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.15);
  overflow: hidden;
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  /* 添加微妙的动画效果 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(255, 199, 44, 0.2);
  }

  /* 任务头部样式 */
  .task-header {
    position: relative;
    padding: 40rpx;
    background: linear-gradient(135deg, #FFC72C 0%, #FFB300 100%);
    overflow: hidden;

    .task-header-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .header-wave {
        position: absolute;
        width: 200%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);

        &.wave1 {
          top: -50%;
          left: -50%;
          border-radius: 45%;
          animation: wave1 6s ease-in-out infinite;
        }

        &.wave2 {
          top: -30%;
          right: -50%;
          border-radius: 40%;
          animation: wave2 8s ease-in-out infinite reverse;
        }
      }
    }

    .task-header-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;

      .day-circle {
        width: 120rpx;
        height: 120rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 60rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        backdrop-filter: blur(10rpx);
        border: 2rpx solid rgba(255, 255, 255, 0.3);

        .day-number {
          font-size: 48rpx;
          font-weight: 700;
          color: #fff;
          line-height: 1;
        }

        .day-text {
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
          margin-top: 4rpx;
        }
      }

      .task-header-info {
        flex: 1;

        .task-category {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 8rpx;
          display: block;
        }

        .task-main-title {
          font-size: 36rpx;
          font-weight: 700;
          color: #fff;
          margin-bottom: 12rpx;
          display: block;
          line-height: 1.3;
        }

        .task-difficulty-indicator {
          display: flex;
          align-items: center;

          .difficulty-star {
            font-size: 20rpx;
            margin-right: 4rpx;
            opacity: 0.4;
            transition: opacity 0.3s ease;

            &.active {
              opacity: 1;
            }
          }

          .difficulty-text {
            font-size: 22rpx;
            color: rgba(255, 255, 255, 0.9);
            margin-left: 8rpx;
            font-weight: 500;
          }
        }
      }
    }
  }

  /* 任务描述区域 */
  .task-description-section {
    padding: 32rpx 40rpx;
    display: flex;
    align-items: flex-start;

    .description-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
      margin-top: 4rpx;

      .icon {
        width: 100%;
        height: 100%;
      }
    }

    .description-content {
      flex: 1;

      .description-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 12rpx;
        display: block;
      }

      .description-text {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        display: block;
      }
    }
  }

  .task-section {
    margin-bottom: 20rpx;

    &.enhanced {
      padding: 32rpx 40rpx;
      margin-bottom: 24rpx;
      border-radius: 24rpx;
      background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);
      border: 1rpx solid rgba(255, 199, 44, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 8rpx 24rpx rgba(255, 199, 44, 0.1);
      }
    }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .section-icon-wrapper {
        display: flex;
        align-items: center;
        padding: 12rpx;
        border-radius: 16rpx;
        background: rgba(255, 199, 44, 0.1);
        margin-right: 16rpx;

        &.tips {
          background: rgba(52, 152, 219, 0.1);
        }

        &.resources {
          background: rgba(155, 89, 182, 0.1);
        }
      }

      .section-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .section-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
      }

      .section-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 20rpx;
        font-weight: 500;
        background: rgba(255, 199, 44, 0.2);
        color: #FFC72C;

        &.tips {
          background: rgba(52, 152, 219, 0.2);
          color: #3498DB;
        }

        &.resources {
          background: rgba(155, 89, 182, 0.2);
          color: #9B59B6;
        }
      }
    }

    .section-content {
      font-size: 26rpx;
      color: #333;
      line-height: 1.6;

      &.tips {
        background: rgba(52, 152, 219, 0.05);
        padding: 24rpx;
        border-radius: 16rpx;
        border-left: 4rpx solid #3498DB;
      }

      &.resources {
        display: flex;
        flex-direction: column;
        gap: 16rpx;
      }
    }
  }

  .task-completion-status {
    display: flex;
    align-items: center;
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
    background: rgba(39, 174, 96, 0.1);
    color: #27AE60;
    margin-top: 20rpx;

    .status-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 12rpx;
    }

    text {
      font-size: 26rpx;
      font-weight: 500;
    }
  }

  /* 增强的进度指示器样式 */
  .task-progress-section.enhanced {
    padding: 32rpx 40rpx;
    background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);
    border-radius: 24rpx;
    border: 1rpx solid rgba(255, 199, 44, 0.1);

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;

      .progress-title-wrapper {
        display: flex;
        align-items: center;

        .progress-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }

        .progress-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .progress-value-wrapper {
        display: flex;
        align-items: baseline;

        .progress-value {
          font-size: 36rpx;
          font-weight: 700;
          color: #FFC72C;
        }

        .progress-separator {
          font-size: 24rpx;
          color: #999;
          margin: 0 8rpx;
        }

        .progress-total {
          font-size: 28rpx;
          color: #666;
        }
      }
    }

    .progress-track.enhanced {
      height: 16rpx;
      background: rgba(255, 199, 44, 0.1);
      border-radius: 8rpx;
      position: relative;
      overflow: hidden;

      .progress-fill.enhanced {
        height: 100%;
        background: linear-gradient(90deg, #FFC72C 0%, #FFB300 50%, #FFA000 100%);
        border-radius: 8rpx;
        position: relative;
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);

        .progress-shine {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%);
          animation: shine 3s infinite ease-in-out;
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, #FFC72C 0%, #FFB300 25%, #FFA000 50%, #FF8F00 75%, #FFC72C 100%);
          background-size: 200% 100%;
          border-radius: 8rpx;
          animation: gradient-flow 4s ease-in-out infinite;
        }
      }

      .progress-marker.enhanced {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);

        .marker-dot.enhanced {
          width: 24rpx;
          height: 24rpx;
          background: #FFC72C;
          border-radius: 50%;
          border: 4rpx solid #fff;
          box-shadow: 0 4rpx 12rpx rgba(255, 199, 44, 0.3);
          position: relative;

          .marker-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            background: rgba(255, 199, 44, 0.3);
            border-radius: 50%;
            animation: pulse 2s infinite;
          }
        }
      }
    }

    .progress-milestones.enhanced {
      display: flex;
      justify-content: space-between;
      margin-top: 16rpx;
      position: relative;

      .milestone.enhanced {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        &.completed {
          .milestone-dot.enhanced {
            background: #FFC72C;
            border-color: #FFB300;
          }

          .milestone-text.enhanced {
            color: #FFC72C;
            font-weight: 600;
          }
        }

        .milestone-dot.enhanced {
          width: 16rpx;
          height: 16rpx;
          background: #E0E0E0;
          border: 2rpx solid #BDBDBD;
          border-radius: 50%;
          margin-bottom: 8rpx;
          transition: all 0.3s ease;
        }

        .milestone-text.enhanced {
          font-size: 20rpx;
          color: #999;
          transition: all 0.3s ease;
        }
      }
    }
  }
}

.challenge-card {
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);

  .challenge-header {
    margin-bottom: 40rpx;

    .challenge-title {
      font-size: 36rpx;
      font-weight: 600;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 12rpx;
      display: block;
    }

    .challenge-period {
      font-size: 26rpx;
      color: #666;
    }
  }

  .progress-bar {
    .progress-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .progress-label {
        font-size: 26rpx;
        color: #666;
      }

      .progress-value {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .progress-track {
      height: 20rpx;
      background: #FFF8F0;
      border-radius: 10rpx;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #FFC72C, #FFB300);
        border-radius: 10rpx;
        transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }
}

.stats-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }

  .stats-item {
    background: #fff;
    border-radius: 32rpx;
    padding: 40rpx 30rpx;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4rpx);
      box-shadow: 0 12rpx 36rpx rgba(255, 199, 44, 0.12);
    }

    .stats-icon {
      width: 96rpx;
      height: 96rpx;
      border-radius: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24rpx;
      background: #FFF8F0;
      
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .stats-value {
      font-size: 44rpx;
      font-weight: 600;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 12rpx;
      display: block;
    }

    .stats-label {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.calendar-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }

  .calendar-card {
    background: #fff;
    border-radius: 32rpx;
    padding: 30rpx 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);

    .weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      margin-bottom: 20rpx;
      text-align: center;

      text {
        font-size: 24rpx;
        color: #999;
        text-align: center;
        font-weight: 500;
      }
    }

    .days-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 8rpx;
      padding: 0;

      .day-item {
        aspect-ratio: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        border-radius: 12rpx;
        min-height: 76rpx;
        transition: all 0.3s ease;
        background: #FFF8F0;
        border: none;

        &.placeholder {
          background: transparent;
          box-shadow: none;
          pointer-events: none;
        }

        &.checked {
          background: linear-gradient(135deg, #FFC72C, #FFB300);
          transform: translateY(-2rpx);
          box-shadow: 0 8rpx 16rpx rgba(255, 199, 44, 0.2);

          .day-text, .month-text {
            color: #fff;
          }

          .month-text {
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            background: rgba(255, 255, 255, 0.9);
            color: #FFC72C;
            font-size: 16rpx;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
            margin: 0;
          }

          .check-mark {
            position: absolute;
            bottom: 8rpx;
            width: 28rpx;
            height: 28rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            image {
              width: 24rpx;
              height: 24rpx;
            }
          }
        }
        
        &.invalid-time {
          background: linear-gradient(135deg, #FF9F1C, #F76E11);
          
          &::after {
            content: '非效';
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            background: rgba(255, 255, 255, 0.9);
            color: #F76E11;
            font-size: 16rpx;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
          }
        }

        &.failed {
          background: #FFF5F5;

          .day-text {
            color: #FF5252;
          }

          .month-text {
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            background: rgba(255, 82, 82, 0.1);
            color: #FF5252;
            font-size: 16rpx;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
            margin: 0;
          }
        }

        &.today {
          background: #FFF8F0;
          border: 2rpx solid #FFC72C;
          transform: scale(1.05);

          .day-text {
            color: #FFC72C;
            font-weight: 600;
          }

          &::after {
            content: '今日';
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            background: #FFC72C;
            color: #fff;
            font-size: 16rpx;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
            transform: scale(0.8);
          }
        }

        &.in-period {
          &:not(.checked):not(.today):not(.failed) {
            &:hover {
              background: #FFF2E6;
              transform: translateY(-2rpx);
            }

            .month-text {
              position: absolute;
              top: -6rpx;
              right: -6rpx;
              background: rgba(255, 199, 44, 0.1);
              color: #FFC72C;
              font-size: 16rpx;
              padding: 4rpx 8rpx;
              border-radius: 6rpx;
              margin: 0;
            }
          }
        }

        &:not(.in-period) {
          background: #F8F8F8;
          opacity: 0.5;
          pointer-events: none;

          .day-text, .month-text {
            color: #999;
          }
        }

        .day-text {
          font-size: 28rpx;
          line-height: 1.2;
          font-weight: 500;
          color: #333;
        }

        .month-text {
          font-size: 20rpx;
          color: #666;
          margin-top: 4rpx;
        }
      }
    }
  }
}

.time-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }

  .time-card {
    background: #fff;
    border-radius: 32rpx;
    padding: 40rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4rpx);
      box-shadow: 0 12rpx 36rpx rgba(255, 199, 44, 0.12);
    }

    .time-icon {
      width: 96rpx;
      height: 96rpx;
      border-radius: 48rpx;
      background: #FFF8F0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .time-info {
      flex: 1;

      .time-value {
        font-size: 36rpx;
        font-weight: 600;
        background: linear-gradient(135deg, #FFC72C, #FFB300);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 12rpx;
        display: block;
      }

      .time-label {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

/* 现代化弹窗 */
.modern-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

.modern-modal {
  width: 640rpx;
  max-width: 90vw;
  position: relative;
  border-radius: 48rpx;
  overflow: hidden;
  animation: scaleIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  .modal-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .modal-gradient {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(138, 43, 226, 0.95) 0%,
        rgba(30, 144, 255, 0.95) 50%,
        rgba(255, 20, 147, 0.95) 100%);
      backdrop-filter: blur(30rpx);
    }

    .celebration-particles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .particle {
        position: absolute;
        width: 12rpx;
        height: 12rpx;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        animation: celebrationFloat 3s ease-in-out infinite;
      }
    }
  }

  &.modal-success .modal-gradient {
    background: linear-gradient(135deg,
      rgba(76, 175, 80, 0.95) 0%,
      rgba(139, 195, 74, 0.95) 50%,
      rgba(205, 220, 57, 0.95) 100%);
  }

  &.modal-warning .modal-gradient {
    background: linear-gradient(135deg,
      rgba(255, 152, 0, 0.95) 0%,
      rgba(255, 193, 7, 0.95) 50%,
      rgba(255, 235, 59, 0.95) 100%);
  }

  &.modal-failed .modal-gradient {
    background: linear-gradient(135deg,
      rgba(244, 67, 54, 0.95) 0%,
      rgba(233, 30, 99, 0.95) 50%,
      rgba(156, 39, 176, 0.95) 100%);
  }

  .modal-content-modern {
    position: relative;
    z-index: 2;
    padding: 80rpx 60rpx 60rpx;
    text-align: center;

    .modal-icon-area {
      margin-bottom: 40rpx;

      .icon-container {
        position: relative;
        width: 160rpx;
        height: 160rpx;
        margin: 0 auto;
        border-radius: 80rpx;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10rpx);

        .modal-emoji {
          font-size: 80rpx;
          z-index: 3;
          position: relative;
        }

        .icon-ring {
          position: absolute;
          top: -10rpx;
          left: -10rpx;
          right: -10rpx;
          bottom: -10rpx;
          border: 4rpx solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          animation: rotate 3s linear infinite;
        }

        .icon-pulse {
          position: absolute;
          top: -20rpx;
          left: -20rpx;
          right: -20rpx;
          bottom: -20rpx;
          border: 2rpx solid rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          animation: pulse 2s ease-in-out infinite;
        }

        &.modal-success {
          background: rgba(76, 175, 80, 0.3);
        }

        &.modal-warning {
          background: rgba(255, 152, 0, 0.3);
        }

        &.modal-failed {
          background: rgba(244, 67, 54, 0.3);
        }
      }
    }

    .modal-text-area {
      margin-bottom: 60rpx;

      .modal-title-modern {
        font-size: 48rpx;
        font-weight: 700;
        color: #fff;
        margin-bottom: 16rpx;
        display: block;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
      }

      .modal-subtitle {
        font-size: 28rpx;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 24rpx;
        display: block;
      }

      .modal-description {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.6;
        display: block;
      }
    }

    .modal-actions {
      .modern-modal-btn {
        width: 100%;
        height: 100rpx;
        position: relative;
        border-radius: 50rpx;
        border: none;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        .btn-bg-modern {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;

          .btn-gradient-modern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(255, 255, 255, 0.3) 0%,
              rgba(255, 255, 255, 0.1) 100%);
            backdrop-filter: blur(10rpx);
          }

          .btn-shine-modern {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
              transparent 0%,
              rgba(255, 255, 255, 0.3) 50%,
              transparent 100%);
            animation: shine 2s ease-in-out infinite;
          }
        }

        .btn-text-modern {
          position: relative;
          z-index: 2;
          font-size: 32rpx;
          font-weight: 700;
          color: #fff;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }

        &.modal-success .btn-gradient-modern {
          background: linear-gradient(135deg,
            rgba(76, 175, 80, 0.8) 0%,
            rgba(139, 195, 74, 0.8) 100%);
        }

        &.modal-warning .btn-gradient-modern {
          background: linear-gradient(135deg,
            rgba(255, 152, 0, 0.8) 0%,
            rgba(255, 193, 7, 0.8) 100%);
        }

        &.modal-failed .btn-gradient-modern {
          background: linear-gradient(135deg,
            rgba(244, 67, 54, 0.8) 0%,
            rgba(233, 30, 99, 0.8) 100%);
        }
      }
    }
  }
}

.success-modal {
  width: 600rpx;
  padding: 50rpx 40rpx;
  background: #fff;
  border-radius: 32rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  
  &.warning-modal {
    .success-title {
      background: linear-gradient(135deg, #FF9F1C, #F76E11);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .modal-btn {
      background: linear-gradient(135deg, #FF9F1C, #F76E11);
    }
  }
  
  &.failed-modal {
    .success-title {
      background: linear-gradient(135deg, #FF9F1C, #F76E11);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .modal-btn {
      background: linear-gradient(135deg, #FF9F1C, #F76E11);
    }
  }
  
  .success-image {
    width: 280rpx;
    height: 280rpx;
    margin: 0 auto 40rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .success-text {
    margin-bottom: 50rpx;
    
    .success-title {
      font-size: 44rpx;
      font-weight: 600;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 20rpx;
      display: block;
    }
    
    .success-desc {
      font-size: 30rpx;
      color: #666;
      line-height: 1.6;
    }
  }
  
  .modal-btn {
    width: 100%;
    height: 96rpx;
    background: linear-gradient(135deg, #FFC72C, #FFB300);
    border-radius: 48rpx;
    color: #fff;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    box-shadow: 0 8rpx 24rpx rgba(255, 199, 44, 0.2);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #FFC72C, #DA291C);
    }
  }
}

.resources {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #FFF8F0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.resource-item:active {
  transform: scale(0.98);
  background: #FFF2E6;
}

.resource-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.resource-text {
  color: #FFA500;
  font-size: 26rpx;
  font-weight: 500;
}

/* 动画效果 */
@keyframes wave1 {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

@keyframes wave2 {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(-180deg) scale(1.2); }
}

/* 增强的任务区块样式 */
.task-section.enhanced {
  margin: 0 40rpx 32rpx;
  border-radius: 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 199, 44, 0.1);

  .section-header {
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, rgba(255, 199, 44, 0.05) 0%, rgba(255, 179, 0, 0.05) 100%);

    .section-icon-wrapper {
      width: 40rpx;
      height: 40rpx;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      &.tips {
        background: linear-gradient(135deg, #4CAF50, #66BB6A);
      }

      &.resources {
        background: linear-gradient(135deg, #2196F3, #42A5F5);
      }

      .section-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      flex: 1;
    }

    .section-badge {
      padding: 4rpx 12rpx;
      background: #FFC72C;
      color: #fff;
      border-radius: 12rpx;
      font-size: 20rpx;
      font-weight: 500;

      &.tips {
        background: transparent;
        color: #4CAF50;
        font-size: 24rpx;
      }

      &.resources {
        background: #2196F3;
      }
    }
  }

  .section-content {
    padding: 24rpx;
    background: #fff;

    &.tips {
      background: linear-gradient(135deg, rgba(76, 175, 80, 0.02) 0%, rgba(102, 187, 106, 0.02) 100%);
      border-left: 4rpx solid #4CAF50;
    }

    &.resources {
      padding: 0;

      .resource-item.enhanced {
        display: flex;
        align-items: center;
        padding: 20rpx 24rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
        transition: background-color 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: rgba(33, 150, 243, 0.05);
        }

        .resource-icon-wrapper {
          width: 40rpx;
          height: 40rpx;
          background: linear-gradient(135deg, #2196F3, #42A5F5);
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16rpx;

          .resource-icon {
            width: 20rpx;
            height: 20rpx;
          }
        }

        .resource-content {
          flex: 1;

          .resource-title {
            font-size: 26rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 4rpx;
            display: block;
          }

          .resource-desc {
            font-size: 22rpx;
            color: #999;
            display: block;
          }
        }

        .resource-arrow {
          .arrow-icon {
            width: 24rpx;
            height: 24rpx;
            opacity: 0.6;
          }
        }
      }
    }
  }
}

/* 任务状态和进度样式 */
.task-status-section {
  padding: 0 40rpx 40rpx;

  .task-completion-status.completed {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1));
    border-radius: 20rpx;
    border: 2rpx solid rgba(76, 175, 80, 0.2);

    .completion-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;

      .status-icon {
        width: 100%;
        height: 100%;
      }
    }

    .completion-content {
      flex: 1;

      .completion-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #4CAF50;
        margin-bottom: 4rpx;
        display: block;
      }

      .completion-desc {
        font-size: 24rpx;
        color: #66BB6A;
        display: block;
      }
    }
  }

  .task-motivation {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: linear-gradient(135deg, rgba(255, 199, 44, 0.1), rgba(255, 179, 0, 0.1));
    border-radius: 20rpx;
    border: 2rpx solid rgba(255, 199, 44, 0.2);

    .motivation-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;

      .motivation-img {
        width: 100%;
        height: 100%;
      }
    }

    .motivation-content {
      flex: 1;

      .motivation-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #FF8F00;
        margin-bottom: 4rpx;
        display: block;
      }

      .motivation-desc {
        font-size: 24rpx;
        color: #FFB300;
        display: block;
        line-height: 1.4;
      }
    }
  }
}

.task-progress-section {
  padding: 32rpx 40rpx 40rpx;
  background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.02) 100%);

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .progress-title {
      font-size: 26rpx;
      font-weight: 500;
      color: #333;
    }

    .progress-value {
      font-size: 24rpx;
      font-weight: 600;
      color: #FFC72C;
    }
  }

  .progress-track {
    position: relative;
    height: 12rpx;
    background: rgba(255, 199, 44, 0.1);
    border-radius: 6rpx;
    overflow: hidden;
    margin-bottom: 24rpx;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #FFC72C 0%, #FFB300 100%);
      border-radius: 6rpx;
      transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .progress-marker {
      position: absolute;
      top: -4rpx;
      transform: translateX(-50%);
      transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);

      .marker-dot {
        width: 20rpx;
        height: 20rpx;
        background: #FFC72C;
        border-radius: 10rpx;
        border: 3rpx solid #fff;
        box-shadow: 0 2rpx 8rpx rgba(255, 199, 44, 0.3);
      }
    }
  }

  .progress-milestones {
    position: relative;
    height: 40rpx;

    .milestone {
      position: absolute;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;

      .milestone-dot {
        width: 12rpx;
        height: 12rpx;
        background: rgba(255, 199, 44, 0.3);
        border-radius: 6rpx;
        margin-bottom: 8rpx;
        transition: background-color 0.3s ease;
      }

      .milestone-text {
        font-size: 20rpx;
        color: #999;
        font-weight: 500;
      }

      &.completed {
        .milestone-dot {
          background: #FFC72C;
        }

        .milestone-text {
          color: #FFC72C;
        }
      }
    }
  }
}

/* 现代化动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(120deg);
  }
  66% {
    transform: translateY(-5px) rotate(240deg);
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-15px) translateX(5px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-8px) translateX(-3px) rotate(180deg);
    opacity: 0.4;
  }
  75% {
    transform: translateY(-12px) translateX(8px) rotate(270deg);
    opacity: 0.7;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) scaleX(0);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) scaleX(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes celebrationFloat {
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 为卡片添加进入动画 */
.today-card.modern-card,
.daily-task-card,
.challenge-card,
.stats-section,
.calendar-section,
.time-section {
  animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation-fill-mode: both;
}

.today-card.modern-card {
  animation-delay: 0.1s;
}

.daily-task-card {
  animation-delay: 0.2s;
}

.challenge-card {
  animation-delay: 0.3s;
}

.stats-section {
  animation-delay: 0.4s;
}

.calendar-section {
  animation-delay: 0.5s;
}

.time-section {
  animation-delay: 0.6s;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .modern-header {
    .header-content {
      padding: 0 24rpx;
      padding-top: var(--status-bar-height, 20px);
    }
  }

  .scroll-content.modern-scroll {
    padding: 24rpx;
  }

  .today-card.modern-card {
    padding: 32rpx;
  }

  .modern-modal {
    width: 90vw;

    .modal-content-modern {
      padding: 60rpx 40rpx 40rpx;
    }
  }
}
</style>
